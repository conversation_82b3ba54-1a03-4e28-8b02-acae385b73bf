import { defineConfig, devices } from '@playwright/test';
import assert from 'assert';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.resolve(__dirname, '.env') });

determineMongoDbURL();
const baseURL = determineBaseURL();

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
    testDir: './tests',
    /* Run tests in files in parallel */
    fullyParallel: true,
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: false,
    /* Retry on CI only */
    retries: process.env.CI ? 3 : 0,
    /* Opt out of parallel tests on CI. */
    // workers: process.env.PLAYWRIGHT_WORKER_COUNT ? parseInt(process.env.PLAYWRIGHT_WORKER_COUNT, 10) : 2,
    workers: 1,
    /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    reporter: process.env.CI ? 'blob' : 'html',
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */

    globalSetup: require.resolve('./setup/global.setup'),

    timeout: 90_000,

    expect: {
        timeout: 10_000,
    },

    use: {
        /* Base URL to use in actions like `await page.goto('/')`. */
        baseURL,
        storageState: 'state.json',
        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: 'on-first-retry',
        launchOptions: {
            slowMo: process.env.SLOW_MO ? 1000 : 0,
        },

        actionTimeout: 30_000, // like click, fill, waitForResponse, etc.
    },

    /* Configure projects for major browsers */
    projects: [
        {
            name: 'chromium',
            use: { ...devices['Desktop Chrome'] },
            fullyParallel: true,
        },
    ],

    /* Run your local dev server before starting the tests */
    // webServer: {
    //   command: 'npm run start',
    //   url: 'http://127.0.0.1:3000',
    //   reuseExistingServer: !process.env.CI,
    // },
});
function determineBaseURL() {
    const { TESTING_TARGET } = process.env;

    switch (TESTING_TARGET) {
        case 'local':
            return 'http://localhost:4200/';
        case 'development':
            return 'https://development.omni.malou.io/';
        case 'staging':
            return 'https://staging.omni.malou.io/';
        case 'production':
            return 'https://app.malou.io/';
        case 'local_dev':
            return 'http://localhost:4200/';
        default:
            throw new Error('TESTING_TARGET is not set');
    }
}

function determineMongoDbURL() {
    const { TESTING_TARGET } = process.env;
    switch (TESTING_TARGET) {
        case 'local':
            process.env.MONGODB_URI = process.env.MONGODB_URI_LOCAL;
            break;
        case 'development':
            process.env.MONGODB_URI = process.env.MONGODB_URI_DEVELOPMENT;
            break;
        case 'staging':
            process.env.MONGODB_URI = process.env.MONGODB_URI_STAGING;
            break;
        case 'production':
            process.env.MONGODB_URI = process.env.MONGODB_URI_PRODUCTION;
            break;
        case 'local_dev': // Use local API connected to development db
            process.env.MONGODB_URI = process.env.MONGODB_URI_DEVELOPMENT;
            break;
        default:
            assert.fail('TESTING_TARGET is not set');
    }
}
