import { expect, it } from 'baseTest';

import { PlatformKey } from '@malou-io/package-utils';

import { getCurrentFacebookInstagramAccount } from ':constants';

const currentAccount = getCurrentFacebookInstagramAccount();

[
    {
        platformKey: PlatformKey.INSTAGRAM,
        accountName: currentAccount.instagramAccountName,
    },
    {
        platformKey: PlatformKey.FACEBOOK,
        accountName: currentAccount.facebookPageName,
    },
].forEach(({ platformKey, accountName }) => {
    it(`should connect platform ${platformKey}`, async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/settings/platforms/connection`);
        const card = await page.getByTestId(`card-${platformKey}`);
        await card.getByTestId(`connect-btn-${platformKey}`).click();
        await page.getByTestId('platforms-connection-account-input').click();
        await page.getByRole('option', { name: currentAccount.facebookAccountName }).click();
        await page.locator('mat-radio-button').getByText(accountName).click();
        await page.getByTestId('modal-primary-button').click();
        await page.waitForResponse(
            async (response) => {
                if (response.url().includes('credentials/permissions') && response.status() === 200) {
                    const bodyText = await response.text();
                    const body = JSON.parse(bodyText);

                    return body.data?.key === platformKey && body.data?.isValid === true;
                }
                return false;
            },
            { timeout: 30000 }
        );
    });
});

[
    {
        platformKey: PlatformKey.TRIPADVISOR,
        pageUrl: 'https://www.tripadvisor.fr/Restaurant_Review-g227617-d15056174-Reviews-Andiamo-Thoiry_Ain_Auvergne_Rhone_Alpes.html',
    },
    {
        platformKey: PlatformKey.FOURSQUARE,
        pageUrl: 'https://foursquare.com/lenina8939702',
    },
    {
        platformKey: PlatformKey.YELP,
        pageUrl:
            'https://www.yelp.fr/biz/ninas-paris?adjust_creative=IEIf37zltcqlYia_9yfjCQ&utm_campaign=yelp_api_v3&utm_medium=api_v3_business_search&utm_source=IEIf37zltcqlYia_9yfjCQ',
    },
].forEach(({ platformKey, pageUrl }) => {
    it(`should connect platform ${platformKey}`, async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/settings/platforms/connection`);
        const card = await page.getByTestId(`card-${platformKey}`);
        await card.getByTestId(`connect-btn-${platformKey}`).click();

        await page.getByTestId('platform-connection-step-0-btn').click();

        const modal = await page.locator('app-modal-structure');

        await modal.getByTestId('modal-long-text-primary-btn').click();

        await page.waitForResponse(async (response) => {
            return response.url().includes(`${platformKey}/search`);
        });

        //const businessItems = await modal.getByTestId('business-selector-item').all();

        //await expect.soft(businessItems.length).toBeGreaterThan(0);

        await modal.getByTestId('select-business-url-input').fill(pageUrl);

        await modal.getByTestId('modal-primary-button').click();

        await page.waitForResponse(/pull/);

        await page.waitForTimeout(1000);

        await modal.getByTestId('modal-primary-button').click();

        await page.waitForTimeout(1000);

        await modal.getByTestId('modal-primary-button').click();

        await expect(await card.getByTestId(`need-review-${platformKey}`)).toBeVisible();
    });
});
[
    {
        platformKey: PlatformKey.LAFOURCHETTE,
        pageUrl: 'https://www.thefork.fr/restaurant/laia-r586197',
        email: '<EMAIL>',
        password: 'StrongPassword!',
    },
].forEach(({ platformKey, pageUrl, email, password }) => {
    it(`should connect platform ${platformKey}`, async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/settings/platforms/connection`);
        const card = await page.getByTestId(`card-${platformKey}`);
        await card.getByTestId(`connect-btn-${platformKey}`).click();

        await page.getByTestId('platform-connection-step-0-btn').click();

        const modal = await page.locator('app-modal-structure');

        await modal.getByTestId('modal-long-text-primary-btn').click();

        await page.waitForResponse(async (response) => {
            return response.url().includes(`${platformKey}/search`);
        });

        //const businessItems = await modal.getByTestId('business-selector-item').all();

        //await expect.soft(businessItems.length).toBeGreaterThan(0);

        await modal.getByTestId('select-business-url-input').fill(pageUrl);

        await modal.getByTestId('modal-primary-button').click();

        await modal.getByTestId('password-managed-connection-email-input').fill(email);
        await modal.getByTestId('password-managed-connection-password-input').fill(password);

        await modal.getByTestId('modal-primary-button').click();

        await page.waitForResponse(/access/i);

        await expect(await card.getByTestId(`need-review-${platformKey}`)).toBeVisible();
    });
});

[
    {
        platformKey: PlatformKey.ZENCHEF,
        apiKey: 'def71fd6-f735-11e9-a9db-0280694512f0',
        email: '<EMAIL>',
        zenchefRestaurantId: '349768',
    },
].forEach(({ platformKey, zenchefRestaurantId }) => {
    it(`should connect platform ${platformKey}`, async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/settings/platforms/connection`);
        const card = await page.getByTestId(`card-${platformKey}`);
        await card.getByTestId(`connect-btn-${platformKey}`).click();

        await page.getByTestId('platform-connection-step-0-btn').click();

        const modal = await page.locator('app-modal-structure');

        await modal.getByTestId('modal-primary-button').click();

        await modal.getByTestId('zenchef-connection-restaurant-id-input').fill(zenchefRestaurantId);

        await modal.getByTestId('modal-primary-button').click();

        await expect(await card.getByTestId(`verified-${platformKey}`)).toBeVisible({
            timeout: 20_000,
        });
    });
});

[
    {
        platformKey: PlatformKey.UBEREATS,
        pageUrl: 'https://merchants.ubereats.com/manager/home/<USER>',
    },
    {
        platformKey: PlatformKey.OPENTABLE,
        pageUrl: 'https://guestcenter.opentable.com/restaurant/292527/home',
    },
].forEach(({ platformKey, pageUrl }) => {
    it(`should connect platform ${platformKey}`, async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/settings/platforms/connection`);
        const card = await page.getByTestId(`card-${platformKey}`);
        await card.getByTestId(`connect-btn-${platformKey}`).click();

        await page.getByTestId('platform-connection-step-0-btn').click();

        const modal = await page.locator('app-modal-structure');

        await modal.getByTestId('modal-primary-button').click();

        await modal.getByTestId('simple-account-managed-connection-select-business-url-input').fill(pageUrl);

        await page.waitForResponse(async (response) => {
            return response.url().includes(`${platformKey}/search`);
        });

        await modal.getByTestId('business-selector-item').first().click();

        await modal.getByTestId('modal-primary-button').click();

        await page.waitForResponse(/platforms/i);

        await expect(await card.getByTestId(`verified-${platformKey}`).or(card.getByTestId(`need-review-${platformKey}`))).toBeVisible();
    });
});
[
    {
        platformKey: PlatformKey.DELIVEROO,
        email: '<EMAIL>',
        password: 'maythai1234',
    },
].forEach(({ platformKey, email, password }) => {
    it(`should connect platform ${platformKey}`, async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/settings/platforms/connection`);
        const card = await page.getByTestId(`card-${platformKey}`);
        await card.getByTestId(`connect-btn-${platformKey}`).click();

        await page.getByTestId('platform-connection-step-0-btn').click();

        let modal = await page.locator('app-modal-structure');

        await modal.getByTestId('deliveroo-connection-email-input').fill(email);
        await modal.getByTestId('deliveroo-connection-password-input').fill(password);

        await modal.getByTestId('modal-primary-button').click();

        await page.waitForResponse(/authenticate/i);

        await page.waitForURL(/credentials\/validate/i);

        await page.getByText('Tests Auto Ne Pas Toucher').click();

        await page.getByTestId('validate-credentials-confirm-btn').click();

        await page.waitForURL(/restaurants/i);

        modal = await page.locator('app-modal-structure');

        await modal.getByTestId('platforms-connection-account-input').click();

        await page.locator(`mat-option:has-text("${email}")`).click();

        await modal.getByTestId('business-selector-item').first().click();

        await modal.getByTestId('modal-primary-button').click();

        await page.waitForResponse(/platforms/i);

        await expect(await card.getByTestId(`verified-${platformKey}`)).toBeVisible({
            timeout: 20_000,
        });
    });
});
