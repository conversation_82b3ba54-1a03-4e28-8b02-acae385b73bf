import { expect, it } from 'baseTest';

import { getCurrentFacebookInstagramAccount } from ':constants';
import { openSocialPostModal } from ':shared/open-social-post';

const currentAccount = getCurrentFacebookInstagramAccount();

it('should set the location of the current restaurant inside the input box', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA_BRAND}/social/socialposts`);
    await openSocialPostModal(page);

    await page.waitForResponse(/facebook\/me\/pages/i);

    await expect(async () => {
        const location = await page.getByTestId('social-post-location-input').inputValue();

        expect(location).toContain(currentAccount.brandRestaurantName);
    }).toPass();
});
it('should edit location', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA_BRAND}/social/socialposts`);
    await openSocialPostModal(page);

    await page.waitForResponse(/facebook\/me\/pages/i);

    await page.getByTestId('social-post-location-input').fill('Musée du louvre');

    await page.waitForResponse(/facebook\/me\/pages/i);

    await page.locator('mat-option').first().click();

    await expect(await page.getByTestId('social-post-location-input').inputValue()).toMatch(/louvre/i);
});

it('should use emoji picker in caption', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA_BRAND}/social/socialposts`);
    await openSocialPostModal(page);

    await page.locator('app-emoji-picker').click();

    await page.locator('ngx-emoji').filter({ hasText: '😍' }).first().click();

    await expect(await page.getByTestId('social-post-caption-input').inputValue()).toContain('😍');
});
