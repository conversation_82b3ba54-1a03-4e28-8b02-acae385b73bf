import { expect, it } from 'baseTest';

it('should translate review using AI', async ({ page }) => {
    await page.goto(
        `/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews?reviewId=${process.env.FACEBOOK_ENGLISH_REVIEW_ID}`
    );

    const selectedReview = page.locator('app-basic-preview').locator('.selected');
    const previousReviewText = await selectedReview.getByTestId('review-model-review-text').textContent();
    const translateBtn = selectedReview.getByTestId('review-translation-btn');

    await translateBtn.click();
    await page.waitForResponse(async (response) => {
        return response.url().includes('/translate') && response.status() === 200;
    });

    const translation = await selectedReview.getByTestId('review-model-review-text').textContent();

    expect(previousReviewText).not.toEqual(translation);
});

it('should translate reply using AI', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews?reviewId=${process.env.HELENE_GMB_REVIEW_ID}`);

    await page.waitForTimeout(5000);

    await page.getByTestId('hide-reply-section-button').click();

    const REPLY_TEXT = 'Bonjour';

    await page.getByTestId('answer-review-text-area-input').fill(REPLY_TEXT);

    await page.evaluate(() => {
        document.querySelector('app-answer-review-modal .malou-dialog__body').scroll(0, 1000);
    });

    await page.getByTestId('ai-generation-translate-btn').click();

    await page.locator('#tracking_answer_review_translate_answer_to_en_language_with_ai_button').click();

    await page.waitForResponse(async (response) => {
        return response.url().includes('/translate') && response.status() === 200;
    });

    const translatedReview = await page.getByTestId('answer-review-text-area-input').inputValue();

    expect(translatedReview).not.toEqual(REPLY_TEXT);

    const langValue = await page.getByTestId('keywords-score-gauge-bricks-lang-input').inputValue();

    expect(langValue).toEqual('Anglais');
});
