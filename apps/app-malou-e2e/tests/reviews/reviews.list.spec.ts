import { expect, it } from 'baseTest';

import { selectMaxDateRangeReviewFilter } from ':shared/select-max-date-range-review-filter';

it('should check if GMB reviews are displayed', async ({ page }) => {
    const textGmbReview = 'Bouffe moyenne mais super accueil !';

    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews`);

    await selectMaxDateRangeReviewFilter({ page });

    const inputReviewsSearch = await page.getByTestId('main-reviews-header-search').getByTestId('reviews-search-input');

    await inputReviewsSearch.click();
    await inputReviewsSearch.fill(textGmbReview);
    const reviewBox = await page.locator('app-review-preview').filter({ hasText: textGmbReview }).first();

    await expect(reviewBox).toBeVisible();
});

it('should archive a review', async ({ page }) => {
    const textGmbReview = 'Ce traiteur est un vrai bijou';
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews`);

    await selectMaxDateRangeReviewFilter({ page });

    const inputReviewsSearch = await page.getByTestId('main-reviews-header-search').getByTestId('reviews-search-input');

    await inputReviewsSearch.click();
    await inputReviewsSearch.fill(textGmbReview);

    await page.locator('app-review-preview').filter({ hasText: textGmbReview }).getByTestId('review-preview-archive-btn').click();

    await page.locator('app-reviews-header-filters').click();

    const menuPanel = await page.locator('.mat-mdc-menu-panel').first();

    await menuPanel.getByTestId('reviews-filters-archived').click();
    await menuPanel.getByTestId('reviews-filters-unarchived').click();

    await expect(await page.locator('app-review-preview').filter({ hasText: textGmbReview })).toBeVisible();
});
