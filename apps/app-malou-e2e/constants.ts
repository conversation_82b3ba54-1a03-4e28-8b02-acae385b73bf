export const E2E_ADMIN_USER = {
    email: '<EMAIL>',
    password: process.env.MALOU_E2E_PASSWORD,
};

export const E2E_BASIC_USER = {
    email: '<EMAIL>',
    password: process.env.MALOU_E2E_PASSWORD,
};

export const ORGANIZATION_NAME = 'Tests Auto Ne Pas Toucher';

export const E2E_GOOGLE_ACCOUNT = {
    email: '<EMAIL>',
    password: process.env.MALOU_E2E_PASSWORD,
};

export const E2E_SOCIAL_ACCOUNT = {
    email: '<EMAIL>',
    password: process.env.MALOU_E2E_PASSWORD,
};

export const E2E_USER_REPORTS = {
    email: '<EMAIL>',
    password: 'StrongPassword!',
};

export const ZENCHEF_CREDENTIALS = {
    email: '<EMAIL>',
    apiKey: 'def71fd6-f735-11e9-a9db-0280694512f0',
};

// Facebook/Instagram account configurations for alternating tests
export const FACEBOOK_INSTAGRAM_ACCOUNTS = {
    ACCOUNT_1: {
        facebookAccountName: 'Lise QA',
        facebookPageName: 'Le Nina QA',
        instagramAccountName: 'leninaqamalou',
        brandRestaurantName: 'Le Nina QA',
    },
    ACCOUNT_2: {
        facebookAccountName: 'Lise Bakk',
        facebookPageName: 'La bonne fourchette du 20',
        instagramAccountName: 'La bonne fourchette',
        brandRestaurantName: 'La bonne fourchette du 20',
    },
};

export function getCurrentFacebookInstagramAccount() {
    const currentDay = new Date().getDate();
    const isOddDay = currentDay % 2 === 1;
    const selectedAccount = isOddDay ? FACEBOOK_INSTAGRAM_ACCOUNTS.ACCOUNT_1 : FACEBOOK_INSTAGRAM_ACCOUNTS.ACCOUNT_2;

    console.info(
        `[E2E] Using ${isOddDay ? 'ACCOUNT_1' : 'ACCOUNT_2'} for day ${currentDay}: ${selectedAccount.facebookAccountName} / ${selectedAccount.brandRestaurantName}`
    );

    return selectedAccount;
}

export const REVIEW_SOCIAL_IDS = {
    FACEBOOK_ENGLISH_REVIEW: '*****************', // Facebook review
    ARCHIVE: 'AbFvOqmQGDDottNPYpYMR_EqXRFjFbxKZNI1wkY604gi4oVJOanxjqjCiaIkayifMefeAt5Pye1csg',
    CLIENTS_MANAGER: 'AbFvOqkoYGFcbsrwYKjZ1WjAuOCg9O5jXSWiVTJ2iyuayT2-zVVG-2Zz7xfJls59fIXMLP9p9Lqq',
    HELENE: 'AbFvOqn2BXLOeiNnr1F7HcW4e-Mj6vUpv6DFbkzqejytYuPNQhYenQiUg8-ZQuUPWdQQkYhHNGq4Mw',
};

export const ONE_MINUTE = 60000;
