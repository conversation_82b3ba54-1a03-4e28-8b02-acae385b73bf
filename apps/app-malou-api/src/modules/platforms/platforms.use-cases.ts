import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { PlatformDto } from '@malou-io/package-dto';
import { DbId, ID, IPlatform, IRestaurantAttribute, toDbId } from '@malou-io/package-models';
import { hasRatingOutOfTen, isNotNil, MalouErrorCode, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { CommentsRepository } from ':modules/comments/comments.repository';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import * as facebookCredentialUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import MentionsRepository from ':modules/mentions/mentions.repository';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { PlatformsDtoMapper } from ':modules/platforms/platforms.dto-mapper';
import { Platform } from ':modules/platforms/platforms.entity';
import { PlatformsGetter } from ':modules/platforms/platforms.getter';
import { SocialIdsResult } from ':modules/platforms/platforms.interface';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SearchSocialIdsUseCase } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.use-case';
import { UpsertPlatformUseCase } from ':modules/platforms/use-cases/upsert-platform/upsert-platform.use-case';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class PlatformsUseCases {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _commentsRepository: CommentsRepository,
        private readonly _credentialsRepository: CredentialsRepository,
        private readonly _mentionsRepository: MentionsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _gmbApiProviderUseCases: GmbApiProviderUseCases,
        private readonly _searchSocialIdsUseCase: SearchSocialIdsUseCase,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _platformsGetter: PlatformsGetter,
        private readonly _platformsDtoMapper: PlatformsDtoMapper,
        private readonly _upsertPlatformUseCase: UpsertPlatformUseCase
    ) {}

    async attachCredential(platformId: string, credentialId: string): Promise<PlatformDto> {
        const updatedPlatform = await this._platformsRepository.findOneAndUpdate({
            filter: { _id: toDbId(platformId) },
            update: { credentials: [toDbId(credentialId)] },
            options: { lean: true },
        });
        if (!updatedPlatform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    platformId,
                },
            });
        }
        return this._platformsDtoMapper.toDto(updatedPlatform);
    }

    async getPlatformByPlatformKeyAndRestaurantId(platformKey: PlatformKey, restaurantId: string): Promise<PlatformDto> {
        const platform = await this._platformsRepository.findOne({
            filter: { key: platformKey, restaurantId },
            options: { lean: true },
        });
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found',
                metadata: {
                    platformKey,
                    restaurantId,
                },
            });
        }
        return this._platformsDtoMapper.toDto(platform);
    }

    async initializePlatform(
        platformKey: PlatformKey.GMB | PlatformKey.FACEBOOK,
        restaurantId: string,
        apiEndpointV2: string | undefined,
        credentialId: string,
        accountId?: string
    ): Promise<IPlatform> {
        const socialId = await this._platformsGetter.getPlatformUseCases(platformKey)?.getSocialId(restaurantId);
        const update: Partial<IPlatform> = { socialId, restaurantId: toDbId(restaurantId) };
        if (platformKey === PlatformKey.GMB && apiEndpointV2) {
            if (accountId) {
                update.apiEndpoint = `${accountId}/${apiEndpointV2}`;
            }
            update.apiEndpointV2 = apiEndpointV2;
        }
        const platform = await this._platformsRepository.upsert({ filter: { restaurantId, key: platformKey }, update });
        await this._platformsRepository.unshiftCredentials(platform._id, credentialId);
        if (platformKey === PlatformKey.FACEBOOK) {
            try {
                const params = {
                    option: { SHOW_PARENT_POSTS_ON_LOCATIONS: 'Never show' },
                    locale: 'en_US',
                };
                await facebookCredentialUseCases.updateBrandPageSettingsAboutPosts(credentialId, socialId, params);
            } catch (err) {
                logger.warn('[ERROR_UPDATING_BRAND_PAGE_SETTINGS]', socialId, err);
            }
        }

        const rawPlatformData = await this.getPlatformDataForRestaurantId(restaurantId, platformKey, credentialId);
        let mappedData;
        if (platformKey === PlatformKey.GMB) {
            assert(apiEndpointV2, 'apiEndpointV2 is undefined');
            const { attributes } = (await this._gmbApiProviderUseCases.getLocationAttributes(credentialId, apiEndpointV2))?.data;
            mappedData = await this.mapPlatformDataToMalou({ ...rawPlatformData, attributes }, platformKey, restaurantId);
        } else {
            mappedData = await this.mapPlatformDataToMalou(rawPlatformData, platformKey, restaurantId);
        }

        return this._upsertPlatformUseCase.execute(restaurantId, platformKey, mappedData);
    }

    async getLatestPlatformRatings({
        restaurantIds,
        platformKeys,
        beforeDate,
    }: {
        restaurantIds: DbId[];
        platformKeys?: PlatformKey[];
        beforeDate?: Date;
    }): Promise<
        {
            restaurantId: ID;
            platformKey: PlatformKey;
            rating: number;
        }[]
    > {
        const platforms = await this._platformsRepository.find({
            filter: {
                restaurantId: { $in: restaurantIds },
                ...(platformKeys && { key: { $in: platformKeys } }),
            },
            projection: { socialId: true, restaurantId: true, key: true },
            options: { lean: true },
        });

        const getRatingOnFive = (rating: number, key: PlatformKey): number => {
            // Rating on 10
            if (hasRatingOutOfTen(key)) {
                return rating / 2;
            }

            return rating;
        };

        return Promise.all(
            platforms.map(async ({ socialId, key, restaurantId }) => {
                const platformRatingInsight = await this._platformInsightsRepository.findOne({
                    filter: {
                        socialId,
                        platformKey: key,
                        metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                        ...(beforeDate && {
                            createdAt: {
                                $lte: beforeDate,
                            },
                        }),
                    },
                    projection: { value: true },
                    // Sort by _id: -1 is equivalent to sorting by descending date
                    options: { sort: { _id: -1 }, lean: true },
                });

                if (platformRatingInsight?.value === undefined || platformRatingInsight?.value === null) {
                    return undefined;
                }

                return {
                    restaurantId,
                    platformKey: key,
                    rating: getRatingOnFive(platformRatingInsight.value, key),
                };
            })
        ).then((results) => results.filter(isNotNil));
    }

    async upsertPlatformWithLinkedComponents(
        restaurantId: string,
        platformKey: PlatformKey,
        platformData,
        userId?: string
    ): Promise<IPlatform> {
        const mappedData = await this.mapPlatformDataToMalou(platformData, platformKey, restaurantId);
        const upsertedPlatform = await this._upsertPlatformUseCase.execute(restaurantId, platformKey, mappedData);
        await this._platformsGetter.getPlatformUseCases(platformKey).upsertLinkedComponents({
            restaurantId,
            platformData,
            platform: upsertedPlatform,
            userId,
        });
        return upsertedPlatform;
    }

    async getPlatformDataForRestaurantId(restaurantId: string, platformKey: PlatformKey, credentialId?: string) {
        const platformFound = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
        if (!platformFound || !platformFound.socialId) {
            if (!credentialId) {
                credentialId = platformFound?.credentials?.[0];
            }
            if (credentialId) {
                return this._refreshSocialId(restaurantId, platformKey, credentialId);
            }
        }
        const platformData = await this._platformsGetter.getPlatformUseCases(platformKey)?.getOverviewData({ restaurantId });
        if (platformData?.sentToSQS) {
            return platformData;
        }
        return this._rejectPlatformData(platformData) || platformData;
    }

    async mapPlatformDataToMalou(platformData, platformKey: PlatformKey, restaurantId: ID) {
        const mappedPlatformData = await this._platformsGetter
            .getPlatformUseCases(platformKey)
            .mapOverviewDataToMalou(platformData, restaurantId.toString());

        if (mappedPlatformData.attributeList?.length) {
            const restAttrPromises: Promise<IRestaurantAttribute | null>[] = [];
            for (const attrData of mappedPlatformData.attributeList.filter((attr) => attr.attributeId !== null)) {
                // link attributes to created restaurant
                restAttrPromises.push(
                    this._restaurantAttributesRepository.upsert({
                        filter: { restaurantId, attributeId: attrData.attributeId },
                        update: { attributeValue: attrData.attributeValue },
                    })
                );
            }
            await Promise.allSettled(restAttrPromises);
        }
        return this._rejectMappedData(mappedPlatformData) || mappedPlatformData;
    }

    async lock(platformId: string, fieldKey: string) {
        const platform = await this._platformsRepository.findOne({
            filter: { _id: toDbId(platformId) },
            projection: { lockFields: 1 },
            options: { lean: true },
        });
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    platformId,
                },
            });
        }
        let lockedFields: string[] = [];
        if (!platform.lockedFields) {
            lockedFields = [fieldKey];
        } else {
            lockedFields.push(fieldKey);
        }

        return this._platformsRepository.findOneAndUpdate({ filter: { _id: toDbId(platformId) }, update: { lockedFields } });
    }

    async unlock(platformId: string, fieldKey: string) {
        const platform = await this._platformsRepository.findOne({ filter: { _id: toDbId(platformId) }, options: { lean: true } });
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    platformId,
                },
            });
        }
        platform.lockedFields = (platform.lockedFields || []).filter((field) => field !== fieldKey);
        return this._platformsRepository.findOneAndUpdate({ filter: { _id: toDbId(platformId) }, update: platform });
    }

    async getSocialIds({
        platformKey,
        restaurantId,
        credentialId,
        socialId,
        limit,
        page,
    }: {
        platformKey: PlatformKey;
        restaurantId?: string | null;
        credentialId?: string;
        socialId?: string;
        limit?: number;
        page?: number;
    }): Promise<SocialIdsResult> {
        const socialIds = await this._searchSocialIdsUseCase.execute(platformKey, {
            restaurantId,
            credentialId,
            socialId,
            limit,
            page,
        });
        return this._rejectSocialIds(socialIds) || socialIds;
    }

    async scrapPlatformEndpoint(platformKey: PlatformKey, endpoint: string) {
        const scrappedData = await this._platformsGetter.getPlatformUseCases(platformKey).scrapPlatformEndpoint(endpoint);
        return this._rejectScrappedData(scrappedData) || scrappedData;
    }

    async deletePlatform(platformId: string) {
        const platform = await this._platformsRepository.getPlatformById(platformId, { key: 1, restaurantId: 1 });
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    platformId,
                },
            });
        }
        const { key: platformKey, restaurantId } = platform;
        await Promise.all([
            this._reviewsRepository.deleteMany({ filter: { key: platformKey, restaurantId } }),
            this._commentsRepository.deleteMany({ filter: { platformKey, restaurantId } }),
            this._mentionsRepository.deleteMany({ filter: { platformKey, restaurantId } }),
            this._postsUseCases.deleteManyPostsAndHandleSideEffects({ key: platformKey, restaurantId }),
            this._restaurantsRepository.deletePlatformAccess(restaurantId.toString(), platformKey),
        ]);

        const deletedPlatform = await this._platformsRepository.deleteOne({ filter: { _id: platformId } });

        return deletedPlatform;
    }

    getPlatformsForRestaurantId(restaurantId: string): Promise<Platform[]> {
        return this._platformsRepository.getPlatformsByRestaurantId(restaurantId);
    }

    async changeCurrentlyUsedCredential(platformKey: PlatformKey, restaurantId: string, credentialId: string) {
        const credential = await this._credentialsRepository.findOne({ filter: { _id: toDbId(credentialId) }, options: { lean: true } });
        if (!credential) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                metadata: {
                    credentialId,
                },
            });
        }
        await this._upsertPlatformUseCase.execute(restaurantId, platformKey, {
            $pull: {
                credentials: {
                    $in: [credential._id],
                },
            },
        });
        await this._upsertPlatformUseCase.execute(restaurantId, platformKey, {
            $push: {
                credentials: {
                    $each: [credential._id],
                    $position: 0,
                },
            },
        });
    }

    getLocationDataForPlatform = ({
        platformKey,
        credentialId,
        socialId,
        apiEndpointV2,
    }: {
        platformKey: PlatformKey;
        credentialId: DbId;
        socialId: string;
        apiEndpointV2?: string;
    }) => this._platformsGetter.getPlatformUseCases(platformKey)?.getLocationData({ credentialId, socialId, apiEndpointV2 });

    getProfileAndCoverMediaForPlatform = ({
        platformKey,
        credentialId,
        socialId,
    }: {
        platformKey: PlatformKey;
        credentialId: string;
        socialId: string;
    }) => this._platformsGetter.getPlatformUseCases(platformKey)?.getProfileAndCoverMedia({ credentialId, socialId });

    private async _refreshSocialId(restaurantId: string, platformKey: PlatformKey, credentialId: string) {
        const socialId = await this._searchSocialIdsUseCase.execute(platformKey, { restaurantId, credentialId });
        if (this._rejectSocialId(socialId)) {
            return this._rejectSocialId(socialId);
        }
        return socialId;
    }

    private _rejectScrappedData(data) {
        if (!data) {
            return { error: true, message: MalouErrorCode.PLATFORM_NO_DATA_IN_RESPONSE };
        }
        if (data.error) {
            return data;
        }
    }

    private _rejectSocialIds(socialIds) {
        if (!socialIds) {
            throw new MalouError(MalouErrorCode.PLATFORM_MISSING_SOCIAL_ID);
        }
        if (socialIds.list?.error) {
            return socialIds;
        }
        if (!Array.isArray(socialIds.list)) {
            throw new MalouError(MalouErrorCode.BAD_REQUEST, { message: 'Social ids is not a list', metadata: { socialIds } });
        }
    }

    private _rejectSocialId(socialId) {
        if (!socialId) {
            return { error: true, message: MalouErrorCode.PLATFORM_MISSING_SOCIAL_ID };
        }

        if (socialId.error) {
            return socialId;
        }

        if (typeof socialId !== 'string') {
            return { error: true, message: MalouErrorCode.BAD_REQUEST, errorData: socialId };
        }
    }

    private _rejectPlatformData(platformData) {
        if (platformData?.error) {
            return platformData;
        }
        if (platformData && !('name' in platformData)) {
            return { error: true, message: MalouErrorCode.PLATFORM_DATA_CRAWLING_ERROR };
        }
    }

    private _rejectMappedData(mappedData) {
        if (mappedData.error) {
            return mappedData;
        }
    }
}
