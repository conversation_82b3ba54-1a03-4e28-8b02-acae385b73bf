import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import { IRestaurant } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

export interface SearchRestaurantsV2Params {
    text?: string;
    fields?: string[];
    limit?: number;
    offset?: number;
    active?: boolean;
}

export interface SearchRestaurantsV2Result {
    data: Partial<IRestaurant>[];
    total: number;
}

@singleton()
export class SearchRestaurantsV2UseCase {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(params: SearchRestaurantsV2Params): Promise<SearchRestaurantsV2Result> {
        const { text, fields = [], limit, offset, active } = params;

        const populateOrganizationStage = [
            {
                $lookup: {
                    from: 'organizations',
                    localField: 'organizationId',
                    foreignField: '_id',
                    as: 'organization',
                },
            },
            {
                $unwind: {
                    path: '$organization',
                },
            },
        ];

        const filter: any = {};

        if (text?.trim()) {
            const searchText = text.trim();

            const diacriticInsensitiveText = toDiacriticInsensitiveRegexString(searchText);

            filter.$or = [
                { name: { $regex: diacriticInsensitiveText, $options: 'i' } },
                { internalName: { $regex: diacriticInsensitiveText, $options: 'i' } },
                { 'address.formattedAddress': { $regex: diacriticInsensitiveText, $options: 'i' } },
                { 'address.locality': { $regex: diacriticInsensitiveText, $options: 'i' } },
                { 'organization.name': { $regex: diacriticInsensitiveText, $options: 'i' } },
            ];
        }

        if (active !== undefined) {
            filter.active = active;
        }

        let fieldsToProject = {
            name: { $first: '$name' },
            address: { $first: '$address' },
        };
        if (fields.length > 0) {
            fieldsToProject = { ...fieldsToProject, ...fields.reduce((acc, field) => ({ ...acc, [field]: { $first: `$${field}` } }), {}) };
        }

        const populateManagersStage = [
            {
                $lookup: {
                    from: 'userrestaurants',
                    localField: '_id',
                    foreignField: 'restaurantId',
                    as: 'userRestaurant',
                },
            },
            { $unwind: { path: '$userRestaurant', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userRestaurant.userId',
                    foreignField: '_id',
                    as: 'manager',
                    pipeline: [{ $project: { _id: 1, email: 1, name: 1, lastname: 1 } }],
                },
            },
            { $unwind: { path: '$manager', preserveNullAndEmptyArrays: true } },
            {
                $group: {
                    _id: '$_id',
                    ...fieldsToProject,
                    createdAt: { $first: '$createdAt' },
                    organization: { $first: '$organization' },
                    managers: {
                        $push: {
                            $cond: [
                                { $gt: ['$manager', null] },
                                {
                                    restaurantId: '$_id',
                                    userId: '$userRestaurant.userId',
                                    user: '$manager',
                                },
                                '$$REMOVE',
                            ],
                        },
                    },
                },
            },
        ];

        const pipeline: any[] = [...populateOrganizationStage, { $match: filter }, ...populateManagersStage];

        pipeline.push({
            $sort: {
                createdAt: -1,
                name: 1,
            },
        });

        if (isNotNil(offset)) {
            pipeline.push({ $skip: offset });
        }

        if (isNotNil(limit)) {
            pipeline.push({ $limit: limit });
        }

        const [restaurants, totalCount] = await Promise.all([
            this._restaurantsRepository.aggregate(pipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
            }),
            this._restaurantsRepository.aggregate([...populateOrganizationStage, { $match: filter }, { $count: 'total' }], {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
            }),
        ]);

        return {
            data: restaurants,
            total: totalCount[0]?.total || 0,
        };
    }
}
